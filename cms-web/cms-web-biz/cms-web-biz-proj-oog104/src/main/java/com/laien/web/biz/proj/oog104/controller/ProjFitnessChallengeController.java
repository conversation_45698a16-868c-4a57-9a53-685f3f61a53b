package com.laien.web.biz.proj.oog104.controller;

import com.laien.web.biz.proj.oog104.request.ProjFitnessChallengeAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessChallengePageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessChallengeUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessChallengeDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessChallengePageVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessChallengeService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.frame.response.PageRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * Fitness Challenge 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/08/15
 */
@Api(tags = "项目管理:fitnessChallenge")
@RestController
@RequestMapping("/proj/fitnessChallenge")
@RequiredArgsConstructor
public class ProjFitnessChallengeController extends ResponseController {

    private final IProjFitnessChallengeService service;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjFitnessChallengePageVO>> page(ProjFitnessChallengePageReq pageReq) {
        return succ(service.page(pageReq, RequestContextUtils.getProjectId()));
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@Valid @RequestBody ProjFitnessChallengeAddReq req) {
        service.save(req, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@Valid @RequestBody ProjFitnessChallengeUpdateReq req) {
        service.update(req, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjFitnessChallengeDetailVO> detail(@PathVariable Integer id) {
        return succ(service.findDetailById(id));
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        service.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        service.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        service.deleteByIds(idList);
        return succ();
    }


}
